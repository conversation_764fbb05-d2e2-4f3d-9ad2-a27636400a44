/**
 * CSS to Tailwind Converter 测试文件
 */

// 导入转换器（根据环境调整导入方式）
let CssToTailwindConverter
try {
  // Node.js 环境
  CssToTailwindConverter = require('./css-to-tailwind-converter.js')
} catch (e) {
  // ES6 模块环境
  // import CssToTailwindConverter from './css-to-tailwind-converter.js'
}

// 测试用例
const testCases = [
  {
    name: '基础间距',
    css: 'padding: 1rem; margin: 0.5rem;',
    expected: ['p-4', 'm-2']
  },
  {
    name: '复合间距',
    css: 'margin: 1rem 2rem;',
    expected: ['my-4', 'mx-8']
  },
  {
    name: '四方向间距',
    css: 'padding: 0.5rem 1rem 1.5rem 0.25rem;',
    expected: ['pt-2', 'pr-4', 'pb-6', 'pl-1']
  },
  {
    name: '布局属性',
    css: 'display: flex; align-items: center; justify-content: space-between;',
    expected: ['flex', 'items-center', 'justify-between']
  },
  {
    name: '定位属性',
    css: 'position: absolute; top: 0; left: 0;',
    expected: ['absolute', 'top-0', 'left-0']
  },
  {
    name: '尺寸属性',
    css: 'width: 100%; height: 50%;',
    expected: ['w-full', 'h-1/2']
  },
  {
    name: '字体属性',
    css: 'font-weight: 700; text-align: center;',
    expected: ['font-bold', 'text-center']
  },
  {
    name: '颜色属性',
    css: 'color: #3b82f6; background-color: #ffffff;',
    expected: ['text-blue-500', 'bg-white']
  },
  {
    name: '负值处理',
    css: 'margin-top: -1rem; margin-left: -0.5rem;',
    expected: ['-mt-4', '-ml-2']
  },
  {
    name: '任意值',
    css: 'padding: 15px; width: 350px;',
    expected: ['p-[15px]', 'w-[350px]']
  },
  {
    name: '边框属性',
    css: 'border-width: 1px; border-radius: 0.5rem;',
    expected: ['border', 'rounded-lg']
  },
  {
    name: '百分比值',
    css: 'width: 25%; height: 75%;',
    expected: ['w-1/4', 'h-3/4']
  }
]

// 运行测试
function runTests() {
  const converter = new CssToTailwindConverter()
  let passed = 0
  let failed = 0
  
  console.log('🧪 开始运行 CSS to Tailwind 转换器测试\n')
  
  testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: ${testCase.name}`)
    console.log(`CSS: ${testCase.css}`)
    
    try {
      const result = converter.convertCssToTailwind(testCase.css)
      console.log(`结果: ${result.join(' ')}`)
      console.log(`期望: ${testCase.expected.join(' ')}`)
      
      // 检查是否包含期望的类名
      const hasAllExpected = testCase.expected.every(expected => 
        result.includes(expected)
      )
      
      if (hasAllExpected) {
        console.log('✅ 通过\n')
        passed++
      } else {
        console.log('❌ 失败\n')
        failed++
      }
    } catch (error) {
      console.log(`❌ 错误: ${error.message}\n`)
      failed++
    }
  })
  
  console.log(`📊 测试结果: ${passed} 通过, ${failed} 失败`)
  return { passed, failed }
}

// 功能测试
function functionalTests() {
  const converter = new CssToTailwindConverter()
  
  console.log('\n🔧 功能测试\n')
  
  // 测试 CSS 对象转换
  console.log('1. CSS 对象转换:')
  const cssObject = {
    padding: '1rem',
    margin: '0.5rem',
    color: '#3b82f6',
    display: 'flex'
  }
  const objectResult = converter.convertCssObject(cssObject)
  console.log('输入:', cssObject)
  console.log('输出:', objectResult.join(' '))
  
  // 测试 CSS 规则转换
  console.log('\n2. CSS 规则转换:')
  const cssRule = '.button { padding: 1rem; background-color: #3b82f6; border-radius: 0.5rem; }'
  const ruleResult = converter.convertCssRule(cssRule)
  console.log('输入:', cssRule)
  console.log('输出:', ruleResult)
  
  // 测试类名优化
  console.log('\n3. 类名优化:')
  const duplicateClasses = ['p-4', 'p-4', 'pt-2', 'pb-6', 'flex', 'block']
  const optimized = converter.optimizeClasses(duplicateClasses)
  console.log('输入:', duplicateClasses.join(' '))
  console.log('优化后:', optimized.join(' '))
  
  // 测试类名验证
  console.log('\n4. 类名验证:')
  const testClasses = ['p-4', 'invalid-class', 'text-[#ff0000]', 'unknown-prefix-value']
  const validation = converter.validateClasses(testClasses)
  console.log('输入:', testClasses.join(' '))
  console.log('有效:', validation.valid.join(' '))
  console.log('无效:', validation.invalid.join(' '))
  
  // 测试建议功能
  console.log('\n5. 建议功能:')
  const suggestions = converter.getSuggestions('padding', '15px')
  console.log('属性: padding, 值: 15px')
  console.log('建议:', suggestions.join(' ') || '无建议')
}

// 性能测试
function performanceTest() {
  const converter = new CssToTailwindConverter()
  
  console.log('\n⚡ 性能测试\n')
  
  const testCss = 'padding: 1rem; margin: 0.5rem; color: #3b82f6; display: flex; align-items: center; justify-content: space-between; width: 100%; height: 50%; font-weight: 700;'
  const iterations = 1000
  
  console.log(`测试 CSS: ${testCss}`)
  console.log(`迭代次数: ${iterations}`)
  
  const startTime = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    converter.convertCssToTailwind(testCss)
  }
  
  const endTime = performance.now()
  const totalTime = endTime - startTime
  const avgTime = totalTime / iterations
  
  console.log(`总时间: ${totalTime.toFixed(2)}ms`)
  console.log(`平均时间: ${avgTime.toFixed(4)}ms`)
  console.log(`每秒转换: ${(1000 / avgTime).toFixed(0)} 次`)
}

// 边界情况测试
function edgeCaseTests() {
  const converter = new CssToTailwindConverter()
  
  console.log('\n🎯 边界情况测试\n')
  
  const edgeCases = [
    { name: '空字符串', css: '' },
    { name: '无效 CSS', css: 'invalid css syntax' },
    { name: '只有分号', css: ';;;' },
    { name: '缺少值', css: 'padding:;' },
    { name: '缺少属性', css: ':1rem;' },
    { name: '特殊字符', css: 'padding: 1rem !important;' },
    { name: '浏览器前缀', css: '-webkit-transform: scale(1.1);' },
    { name: 'CSS 变量', css: 'color: var(--primary-color);' },
    { name: 'calc 函数', css: 'width: calc(100% - 2rem);' }
  ]
  
  edgeCases.forEach(testCase => {
    console.log(`测试: ${testCase.name}`)
    console.log(`CSS: "${testCase.css}"`)
    
    try {
      const result = converter.convertCssToTailwind(testCase.css)
      console.log(`结果: ${result.join(' ') || '(空)'}`)
    } catch (error) {
      console.log(`错误: ${error.message}`)
    }
    console.log()
  })
}

// 主函数
function main() {
  if (typeof CssToTailwindConverter === 'undefined') {
    console.error('❌ 无法加载 CssToTailwindConverter')
    return
  }
  
  // 运行所有测试
  const testResults = runTests()
  functionalTests()
  performanceTest()
  edgeCaseTests()
  
  // 总结
  console.log('\n📋 测试总结')
  console.log('='.repeat(50))
  console.log(`基础测试: ${testResults.passed}/${testResults.passed + testResults.failed} 通过`)
  console.log('功能测试: 已完成')
  console.log('性能测试: 已完成')
  console.log('边界测试: 已完成')
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有测试通过！')
  } else {
    console.log(`\n⚠️  有 ${testResults.failed} 个测试失败，请检查实现`)
  }
}

// 运行测试
if (typeof module !== 'undefined' && require.main === module) {
  main()
} else if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runCssToTailwindTests = main
}

// 导出测试函数
if (typeof module !== 'undefined') {
  module.exports = { runTests, functionalTests, performanceTest, edgeCaseTests, main }
}
