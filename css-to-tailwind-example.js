/**
 * CSS to Tailwind Converter 使用示例
 */

import CssToTailwindConverter from './css-to-tailwind-converter.js'

// 创建转换器实例
const converter = new CssToTailwindConverter()

// 示例 1: 基础样式转换
console.log('=== 基础样式转换 ===')
const basicCss = 'padding: 1rem; margin: 0.5rem; color: #3b82f6;'
const basicClasses = converter.convertCssToTailwind(basicCss)
console.log('CSS:', basicCss)
console.log('Tailwind:', basicClasses.join(' '))
// 输出: p-4 m-2 text-blue-500

// 示例 2: 布局样式
console.log('\n=== 布局样式 ===')
const layoutCss = 'display: flex; align-items: center; justify-content: space-between;'
const layoutClasses = converter.convertCssToTailwind(layoutCss)
console.log('CSS:', layoutCss)
console.log('Tailwind:', layoutClasses.join(' '))
// 输出: flex items-center justify-between

// 示例 3: 复合属性
console.log('\n=== 复合属性 ===')
const compoundCss = 'margin: 1rem 2rem; padding: 0.5rem 1rem 1.5rem 0.25rem;'
const compoundClasses = converter.convertCssToTailwind(compoundCss)
console.log('CSS:', compoundCss)
console.log('Tailwind:', compoundClasses.join(' '))
// 输出: my-4 mx-8 pt-2 pr-4 pb-6 pl-1

// 示例 4: 尺寸和定位
console.log('\n=== 尺寸和定位 ===')
const sizingCss = 'width: 100%; height: 50%; position: absolute; top: 0; left: 0;'
const sizingClasses = converter.convertCssToTailwind(sizingCss)
console.log('CSS:', sizingCss)
console.log('Tailwind:', sizingClasses.join(' '))
// 输出: w-full h-1/2 absolute top-0 left-0

// 示例 5: 字体和文本
console.log('\n=== 字体和文本 ===')
const textCss = 'font-weight: 700; text-align: center; color: #ef4444;'
const textClasses = converter.convertCssToTailwind(textCss)
console.log('CSS:', textCss)
console.log('Tailwind:', textClasses.join(' '))
// 输出: font-bold text-center text-red-500

// 示例 6: 背景和边框
console.log('\n=== 背景和边框 ===')
const bgCss = 'background-color: #ffffff; border-width: 1px; border-radius: 0.5rem;'
const bgClasses = converter.convertCssToTailwind(bgCss)
console.log('CSS:', bgCss)
console.log('Tailwind:', bgClasses.join(' '))
// 输出: bg-white border rounded-lg

// 示例 7: 任意值处理
console.log('\n=== 任意值处理 ===')
const arbitraryCss = 'padding: 15px; color: #ff6b35; width: 350px;'
const arbitraryClasses = converter.convertCssToTailwind(arbitraryCss)
console.log('CSS:', arbitraryCss)
console.log('Tailwind:', arbitraryClasses.join(' '))
// 输出: p-[15px] text-[#ff6b35] w-[350px]

// 示例 8: 负值处理
console.log('\n=== 负值处理 ===')
const negativeCss = 'margin-top: -1rem; margin-left: -0.5rem;'
const negativeClasses = converter.convertCssToTailwind(negativeCss)
console.log('CSS:', negativeCss)
console.log('Tailwind:', negativeClasses.join(' '))
// 输出: -mt-4 -ml-2

// 示例 9: 完整的组件样式
console.log('\n=== 完整组件样式 ===')
const componentCss = `
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  margin: 1rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-width: 24rem;
`.replace(/\s+/g, ' ').trim()

const componentClasses = converter.convertCssToTailwind(componentCss)
console.log('CSS:', componentCss)
console.log('Tailwind:', componentClasses.join(' '))
// 输出: flex flex-col items-center p-8 m-4 bg-white rounded-lg shadow-md max-w-96

// 工具函数：批量转换 CSS 规则
function convertCssRules(cssRules) {
  console.log('\n=== 批量转换 CSS 规则 ===')
  
  const rules = [
    'display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;',
    'position: fixed; top: 0; right: 0; z-index: 50;',
    'opacity: 0.5; transform: scale(1.1); transition: all 0.3s ease;',
    'font-size: 1.125rem; line-height: 1.75; font-weight: 500;'
  ]
  
  rules.forEach((rule, index) => {
    const classes = converter.convertCssToTailwind(rule)
    console.log(`规则 ${index + 1}:`)
    console.log('  CSS:', rule)
    console.log('  Tailwind:', classes.join(' '))
    console.log()
  })
}

convertCssRules()

// 高级用法：自定义转换器
class ExtendedConverter extends CssToTailwindConverter {
  constructor() {
    super()
    
    // 扩展颜色映射
    this.colorMap = {
      ...this.colorMap,
      '#ff6b35': 'orange-custom',
      '#4ecdc4': 'teal-custom'
    }
    
    // 添加自定义属性映射
    this.propertyMap = {
      ...this.propertyMap,
      'backdrop-filter': 'backdrop',
      'filter': 'filter'
    }
  }
  
  // 重写颜色处理以支持自定义颜色
  handleColorProperty(prefix, value) {
    const normalizedColor = this.normalizeColor(value)
    const tailwindColor = this.colorMap[normalizedColor]
    
    if (tailwindColor) {
      return [`${prefix}-${tailwindColor}`]
    }
    
    // 尝试匹配最接近的预设颜色
    const closestColor = this.findClosestColor(normalizedColor)
    if (closestColor) {
      return [`${prefix}-${closestColor}`]
    }
    
    return [`${prefix}-[${value}]`]
  }
  
  // 查找最接近的颜色
  findClosestColor(targetColor) {
    // 简化的颜色匹配逻辑
    // 实际应用中可以使用更复杂的颜色距离算法
    const commonColors = {
      '#000000': 'black',
      '#ffffff': 'white',
      '#ef4444': 'red-500',
      '#3b82f6': 'blue-500',
      '#10b981': 'green-500'
    }
    
    return commonColors[targetColor] || null
  }
}

// 使用扩展转换器
console.log('\n=== 扩展转换器示例 ===')
const extendedConverter = new ExtendedConverter()
const customCss = 'color: #ff6b35; background-color: #4ecdc4;'
const customClasses = extendedConverter.convertCssToTailwind(customCss)
console.log('CSS:', customCss)
console.log('Tailwind:', customClasses.join(' '))

// 性能测试
function performanceTest() {
  console.log('\n=== 性能测试 ===')
  
  const testCss = 'padding: 1rem; margin: 0.5rem; color: #3b82f6; display: flex; align-items: center;'
  const iterations = 10000
  
  console.time('转换性能测试')
  for (let i = 0; i < iterations; i++) {
    converter.convertCssToTailwind(testCss)
  }
  console.timeEnd('转换性能测试')
  
  console.log(`完成 ${iterations} 次转换`)
}

performanceTest()
