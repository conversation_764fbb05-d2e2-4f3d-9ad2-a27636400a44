import { run, html, css } from './util/run'

it('can block classes matched literally', () => {
  let config = {
    content: [
      {
        raw: html`<div
          class="font-bold uppercase sm:hover:text-sm hover:text-sm bg-red-500/50 my-custom-class"
        ></div>`,
      },
    ],
    blocklist: ['font', 'uppercase', 'hover:text-sm', 'bg-red-500/50', 'my-custom-class'],
  }

  let input = css`
    @tailwind utilities;
    .my-custom-class {
      color: red;
    }
  `

  return run(input, config).then((result) => {
    return expect(result.css).toMatchFormattedCss(css`
      .font-bold {
        font-weight: 700;
      }
      .my-custom-class {
        color: red;
      }
      @media (min-width: 640px) {
        .sm\:hover\:text-sm:hover {
          font-size: 0.875rem;
          line-height: 1.25rem;
        }
      }
    `)
  })
})

it('can block classes inside @layer', () => {
  let config = {
    content: [
      {
        raw: html`<div class="font-bold my-custom-class"></div>`,
      },
    ],
    blocklist: ['my-custom-class'],
  }

  let input = css`
    @tailwind utilities;
    @layer utilities {
      .my-custom-class {
        color: red;
      }
    }
  `

  return run(input, config).then((result) => {
    return expect(result.css).toMatchFormattedCss(css`
      .font-bold {
        font-weight: 700;
      }
    `)
  })
})

it('blocklists do NOT support regexes', async () => {
  let config = {
    content: [{ raw: html`<div class="font-bold bg-[#f00d1e]"></div>` }],
    blocklist: [/^bg-\[[^]+\]$/],
  }

  let result = await run('@tailwind utilities', config)

  expect(result.css).toMatchFormattedCss(css`
    .bg-\[\#f00d1e\] {
      --tw-bg-opacity: 1;
      background-color: rgb(240 13 30 / var(--tw-bg-opacity, 1));
    }
    .font-bold {
      font-weight: 700;
    }
  `)

  expect().toHaveBeenWarnedWith(['blocklist-invalid'])
})

it('can block classes generated by the safelist', () => {
  let config = {
    content: [{ raw: html`<div class="font-bold"></div>` }],
    safelist: [{ pattern: /^bg-red-(400|500)$/ }],
    blocklist: ['bg-red-500'],
  }

  return run('@tailwind utilities', config).then((result) => {
    expect(result.css).toMatchFormattedCss(css`
      .bg-red-400 {
        --tw-bg-opacity: 1;
        background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
      }
      .font-bold {
        font-weight: 700;
      }
    `)
  })
})
