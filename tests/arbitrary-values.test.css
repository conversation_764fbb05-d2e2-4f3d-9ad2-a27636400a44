.inset-\[11px\] {
  inset: 11px;
}
.inset-\[var\(--value\)\] {
  inset: var(--value);
}
.inset-x-\[11px\] {
  left: 11px;
  right: 11px;
}
.inset-x-\[var\(--value\)\] {
  left: var(--value);
  right: var(--value);
}
.inset-y-\[11px\] {
  top: 11px;
  bottom: 11px;
}
.inset-y-\[var\(--value\)\] {
  top: var(--value);
  bottom: var(--value);
}
.bottom-\[11px\] {
  bottom: 11px;
}
.bottom-\[var\(--value\)\] {
  bottom: var(--value);
}
.left-\[11px\] {
  left: 11px;
}
.left-\[var\(--value\)\] {
  left: var(--value);
}
.right-\[11px\] {
  right: 11px;
}
.right-\[var\(--value\)\] {
  right: var(--value);
}
.top-\[11px\] {
  top: 11px;
}
.top-\[var\(--value\)\] {
  top: var(--value);
}
.z-\[123\] {
  z-index: 123;
}
.z-\[var\(--value\)\] {
  z-index: var(--value);
}
.order-\[4\] {
  order: 4;
}
.order-\[var\(--value\)\] {
  order: var(--value);
}
.col-\[7\] {
  grid-column: 7;
}
.col-start-\[7\] {
  grid-column-start: 7;
}
.col-end-\[7\] {
  grid-column-end: 7;
}
.row-\[7\] {
  grid-row: 7;
}
.row-start-\[7\] {
  grid-row-start: 7;
}
.row-end-\[7\] {
  grid-row-end: 7;
}
.m-\[7px\] {
  margin: 7px;
}
.mx-\[7px\] {
  margin-left: 7px;
  margin-right: 7px;
}
.my-\[7px\] {
  margin-top: 7px;
  margin-bottom: 7px;
}
.mb-\[7px\] {
  margin-bottom: 7px;
}
.ml-\[7px\] {
  margin-left: 7px;
}
.mr-\[7px\] {
  margin-right: 7px;
}
.mt-\[7px\] {
  margin-top: 7px;
}
.mt-\[clamp\(30px\2c 100px\)\] {
  margin-top: clamp(30px, 100px);
}
.aspect-\[16\/9\] {
  aspect-ratio: 16/9;
}
.aspect-\[var\(--aspect\)\] {
  aspect-ratio: var(--aspect);
}
.h-\[3\.23rem\] {
  height: 3.23rem;
}
.h-\[calc\(100\%\+1rem\)\] {
  height: calc(100% + 1rem);
}
.h-\[var\(--height\)\] {
  height: var(--height);
}
.max-h-\[3\.23rem\] {
  max-height: 3.23rem;
}
.max-h-\[calc\(100\%\+1rem\)\] {
  max-height: calc(100% + 1rem);
}
.max-h-\[var\(--height\)\] {
  max-height: var(--height);
}
.min-h-\[3\.23rem\] {
  min-height: 3.23rem;
}
.min-h-\[calc\(100\%\+1rem\)\] {
  min-height: calc(100% + 1rem);
}
.min-h-\[var\(--height\)\] {
  min-height: var(--height);
}
.w-\[\'\)\(\)\'\] {
  width: ')()';
}
.w-\[\'\]\[\]\'\] {
  width: '][]';
}
.w-\[\'\}\{\}\'\] {
  width: '}{}';
}
.w-\[\(\(\)\)\] {
  width: (());
}
.w-\[\(\)\] {
  width: ();
}
.w-\[0\] {
  width: 0;
}
.w-\[3\.23rem\] {
  width: 3.23rem;
}
.w-\[\[\[\]\]\] {
  width: [[]];
}
.w-\[\[\]\] {
  width: [];
}
.w-\[calc\(100\%\+1rem\)\] {
  width: calc(100% + 1rem);
}
.w-\[calc\(100\%\/3-1rem\*2\)\] {
  width: calc(100% / 3 - 1rem * 2);
}
.w-\[calc\(var\(--10-10px\2c calc\(-20px-\(-30px--40px\)\)\)-50px\)\] {
  width: calc(var(--10-10px, calc(-20px - (-30px - -40px))) - 50px);
}
.w-\[var\(--width\)\] {
  width: var(--width);
}
.w-\[var\(--width\2c calc\(100\%\+1rem\)\)\] {
  width: var(--width, calc(100% + 1rem));
}
.w-\[\{\{\}\}\] {
  width: {
     {
    }
  }
}
.w-\[\{\}\] {
  width: {
  }
}
.min-w-\[3\.23rem\] {
  min-width: 3.23rem;
}
.min-w-\[calc\(1-\(\(12-3\)\*0\.5\)\)\] {
  min-width: calc(1 - ((12 - 3) * 0.5));
}
.min-w-\[calc\(1-\(var\(--something\)\*0\.5\)\)\] {
  min-width: calc(1 - (var(--something) * 0.5));
}
.min-w-\[calc\(1-var\(--something\)\*0\.5\)\] {
  min-width: calc(1 - var(--something) * 0.5);
}
.min-w-\[calc\(100\%\+1rem\)\] {
  min-width: calc(100% + 1rem);
}
.min-w-\[var\(--width\)\] {
  min-width: var(--width);
}
.max-w-\[3\.23rem\] {
  max-width: 3.23rem;
}
.max-w-\[calc\(100\%\+1rem\)\] {
  max-width: calc(100% + 1rem);
}
.max-w-\[var\(--width\)\] {
  max-width: var(--width);
}
.flex-\[var\(--flex\)\] {
  flex: var(--flex);
}
.flex-shrink-\[var\(--shrink\)\] {
  flex-shrink: var(--shrink);
}
.shrink-\[var\(--shrink\)\] {
  flex-shrink: var(--shrink);
}
.flex-grow-\[var\(--grow\)\] {
  flex-grow: var(--grow);
}
.grow-\[var\(--grow\)\] {
  flex-grow: var(--grow);
}
.basis-\[var\(--basis\)\] {
  flex-basis: var(--basis);
}
.origin-\[50px_50px\] {
  transform-origin: 50px 50px;
}
.translate-x-\[12\%\] {
  --tw-translate-x: 12%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.translate-x-\[var\(--value\)\] {
  --tw-translate-x: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.translate-y-\[12\%\] {
  --tw-translate-y: 12%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.translate-y-\[var\(--value\)\] {
  --tw-translate-y: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.rotate-\[1\.5turn\] {
  --tw-rotate: 1.5turn;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.rotate-\[2\.3rad\] {
  --tw-rotate: 2.3rad;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.rotate-\[23deg\] {
  --tw-rotate: 23deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.rotate-\[401grad\] {
  --tw-rotate: 401grad;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.skew-x-\[3px\] {
  --tw-skew-x: 3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.skew-x-\[var\(--value\)\] {
  --tw-skew-x: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.skew-y-\[3px\] {
  --tw-skew-y: 3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.skew-y-\[var\(--value\)\] {
  --tw-skew-y: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.scale-\[0\.7\] {
  --tw-scale-x: 0.7;
  --tw-scale-y: 0.7;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.scale-\[var\(--value\)\] {
  --tw-scale-x: var(--value);
  --tw-scale-y: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.scale-x-\[0\.7\] {
  --tw-scale-x: 0.7;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.scale-x-\[var\(--value\)\] {
  --tw-scale-x: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.scale-y-\[0\.7\] {
  --tw-scale-y: 0.7;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.scale-y-\[var\(--value\)\] {
  --tw-scale-y: var(--value);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.animate-\[pong_1s_cubic-bezier\(0\2c 0\2c 0\.2\2c 1\)_infinite\] {
  animation: pong 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
.animate-\[var\(--value\)\] {
  animation: var(--value);
}
.cursor-\[pointer\] {
  cursor: pointer;
}
.cursor-\[url\(\'\.\/path_to_hand\.cur\'\)_2_2\2c pointer\] {
  cursor: url('./path_to_hand.cur') 2 2, pointer;
}
.cursor-\[url\(hand\.cur\)_2_2\2c pointer\] {
  cursor: url(hand.cur) 2 2, pointer;
}
.cursor-\[var\(--value\)\] {
  cursor: var(--value);
}
.scroll-m-\[7px\] {
  scroll-margin: 7px;
}
.scroll-mx-\[7px\] {
  scroll-margin-left: 7px;
  scroll-margin-right: 7px;
}
.scroll-my-\[7px\] {
  scroll-margin-top: 7px;
  scroll-margin-bottom: 7px;
}
.scroll-mb-\[7px\] {
  scroll-margin-bottom: 7px;
}
.scroll-ml-\[7px\] {
  scroll-margin-left: 7px;
}
.scroll-mr-\[7px\] {
  scroll-margin-right: 7px;
}
.scroll-mt-\[7px\] {
  scroll-margin-top: 7px;
}
.scroll-mt-\[var\(--scroll-margin\)\] {
  scroll-margin-top: var(--scroll-margin);
}
.scroll-p-\[7px\] {
  scroll-padding: 7px;
}
.scroll-px-\[7px\] {
  scroll-padding-left: 7px;
  scroll-padding-right: 7px;
}
.scroll-py-\[7px\] {
  scroll-padding-top: 7px;
  scroll-padding-bottom: 7px;
}
.scroll-pb-\[7px\] {
  scroll-padding-bottom: 7px;
}
.scroll-pl-\[7px\] {
  scroll-padding-left: 7px;
}
.scroll-pr-\[7px\] {
  scroll-padding-right: 7px;
}
.scroll-pt-\[7px\] {
  scroll-padding-top: 7px;
}
.scroll-pt-\[var\(--scroll-padding\)\] {
  scroll-padding-top: var(--scroll-padding);
}
.list-\[\'\\1f44d\'\] {
  list-style-type: '\1f44d';
}
.list-\[var\(--value\)\] {
  list-style-type: var(--value);
}
.list-image-\[url\(\.\/my-image\.png\)\] {
  list-style-image: url(./my-image.png);
}
.list-image-\[var\(--value\)\] {
  list-style-image: var(--value);
}
.columns-\[20\] {
  columns: 20;
}
.columns-\[var\(--columns\)\] {
  columns: var(--columns);
}
.auto-cols-\[minmax\(10px\2c auto\)\] {
  grid-auto-columns: minmax(10px, auto);
}
.auto-rows-\[minmax\(10px\2c auto\)\] {
  grid-auto-rows: minmax(10px, auto);
}
.grid-cols-\[200px\2c repeat\(auto-fill\2c minmax\(15\%\2c 100px\)\)\2c 300px\] {
  grid-template-columns: 200px repeat(auto-fill, minmax(15%, 100px)) 300px;
}
.grid-cols-\[\[linename\]\2c 1fr\2c auto\] {
  grid-template-columns: [linename] 1fr auto;
}
.grid-rows-\[200px\2c repeat\(auto-fill\2c minmax\(15\%\2c 100px\)\)\2c 300px\] {
  grid-template-rows: 200px repeat(auto-fill, minmax(15%, 100px)) 300px;
}
.gap-\[20px\] {
  gap: 20px;
}
.gap-\[var\(--value\)\] {
  gap: var(--value);
}
.gap-x-\[20px\] {
  column-gap: 20px;
}
.gap-x-\[var\(--value\)\] {
  column-gap: var(--value);
}
.gap-y-\[20px\] {
  row-gap: 20px;
}
.gap-y-\[var\(--value\)\] {
  row-gap: var(--value);
}
.space-x-\[20cm\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(20cm * var(--tw-space-x-reverse));
  margin-left: calc(20cm * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[calc\(20\%-1cm\)\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(calc(20% - 1cm) * var(--tw-space-x-reverse));
  margin-left: calc(calc(20% - 1cm) * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-\[20cm\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(20cm * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(20cm * var(--tw-space-y-reverse));
}
.space-y-\[calc\(20\%-1cm\)\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(calc(20% - 1cm) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(calc(20% - 1cm) * var(--tw-space-y-reverse));
}
.divide-x-\[20cm\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(20cm * var(--tw-divide-x-reverse));
  border-left-width: calc(20cm * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-x-\[calc\(20\%-1cm\)\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(calc(20% - 1cm) * var(--tw-divide-x-reverse));
  border-left-width: calc(calc(20% - 1cm) * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y-\[20cm\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(20cm * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(20cm * var(--tw-divide-y-reverse));
}
.divide-y-\[calc\(20\%-1cm\)\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(calc(20% - 1cm) * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(calc(20% - 1cm) * var(--tw-divide-y-reverse));
}
.divide-\[black\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-divide-opacity, 1));
}
.divide-\[var\(--value\)\] > :not([hidden]) ~ :not([hidden]) {
  border-color: var(--value);
}
.divide-opacity-\[0\.8\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 0.8;
}
.divide-opacity-\[var\(--value\)\] > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: var(--value);
}
.rounded-\[11px\] {
  border-radius: 11px;
}
.rounded-b-\[var\(--radius\)\] {
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}
.rounded-l-\[var\(--radius\)\] {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}
.rounded-r-\[var\(--radius\)\] {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}
.rounded-t-\[var\(--radius\)\] {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.rounded-bl-\[var\(--radius\)\] {
  border-bottom-left-radius: var(--radius);
}
.rounded-br-\[var\(--radius\)\] {
  border-bottom-right-radius: var(--radius);
}
.rounded-tl-\[var\(--radius\)\] {
  border-top-left-radius: var(--radius);
}
.rounded-tr-\[var\(--radius\)\] {
  border-top-right-radius: var(--radius);
}
.border-\[2\.5px\] {
  border-width: 2.5px;
}
.border-\[length\:var\(--value\)\] {
  border-width: var(--value);
}
.border-b-\[2\.5px\] {
  border-bottom-width: 2.5px;
}
.border-b-\[length\:var\(--value\)\] {
  border-bottom-width: var(--value);
}
.border-l-\[2\.5px\] {
  border-left-width: 2.5px;
}
.border-l-\[length\:var\(--value\)\] {
  border-left-width: var(--value);
}
.border-r-\[2\.5px\] {
  border-right-width: 2.5px;
}
.border-r-\[length\:var\(--value\)\] {
  border-right-width: var(--value);
}
.border-t-\[2\.5px\] {
  border-top-width: 2.5px;
}
.border-t-\[length\:var\(--value\)\] {
  border-top-width: var(--value);
}
.border-\[\#f00\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 0 / var(--tw-border-opacity, 1));
}
.border-\[color\:var\(--value\)\] {
  border-color: var(--value);
}
.border-\[red_black\] {
  border-color: red black;
}
.border-b-\[\#f00\] {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 0 0 / var(--tw-border-opacity, 1));
}
.border-b-\[color\:var\(--value\)\] {
  border-bottom-color: var(--value);
}
.border-l-\[\#f00\] {
  --tw-border-opacity: 1;
  border-left-color: rgb(255 0 0 / var(--tw-border-opacity, 1));
}
.border-l-\[color\:var\(--value\)\] {
  border-left-color: var(--value);
}
.border-r-\[\#f00\] {
  --tw-border-opacity: 1;
  border-right-color: rgb(255 0 0 / var(--tw-border-opacity, 1));
}
.border-r-\[color\:var\(--value\)\] {
  border-right-color: var(--value);
}
.border-t-\[\#f00\] {
  --tw-border-opacity: 1;
  border-top-color: rgb(255 0 0 / var(--tw-border-opacity, 1));
}
.border-t-\[color\:var\(--value\)\] {
  border-top-color: var(--value);
}
.border-opacity-\[0\.8\] {
  --tw-border-opacity: 0.8;
}
.border-opacity-\[var\(--value\)\] {
  --tw-border-opacity: var(--value);
}
.bg-\[\#0000ffcc\] {
  background-color: #0000ffcc;
}
.bg-\[\#0f0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 255 0 / var(--tw-bg-opacity, 1));
}
.bg-\[\#0f0_var\(--value\)\] {
  background-color: #0f0 var(--value);
}
.bg-\[\#ff0000\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 0 / var(--tw-bg-opacity, 1));
}
.bg-\[color\:var\(--value1\)_var\(--value2\)\] {
  background-color: var(--value1) var(--value2);
}
.bg-\[hsl\(0\2c 100\%\2c 50\%\)\] {
  --tw-bg-opacity: 1;
  background-color: hsl(0 100% 50% / var(--tw-bg-opacity, 1));
}
.bg-\[hsl\(0rad\2c 100\%\2c 50\%\)\] {
  --tw-bg-opacity: 1;
  background-color: hsl(0rad 100% 50% / var(--tw-bg-opacity, 1));
}
.bg-\[hsla\(0\2c 100\%\2c 50\%\2c 0\.3\)\] {
  background-color: hsla(0, 100%, 50%, 0.3);
}
.bg-\[hsla\(0turn\2c 100\%\2c 50\%\2c 0\.3\)\] {
  background-color: hsla(0turn, 100%, 50%, 0.3);
}
.bg-\[rgb\(123\2c 123\2c 123\)\] {
  --tw-bg-opacity: 1;
  background-color: rgb(123 123 123 / var(--tw-bg-opacity, 1));
}
.bg-\[rgb\(123\2c _456\2c _123\)_black\] {
  background-color: rgb(123, 456, 123) black;
}
.bg-\[rgb\(123_456_789\)\] {
  --tw-bg-opacity: 1;
  background-color: rgb(123 456 789 / var(--tw-bg-opacity, 1));
}
.bg-\[rgba\(123\2c 123\2c 123\2c 0\.5\)\] {
  background-color: rgba(123, 123, 123, 0.5);
}
.bg-\[var\(--value\)\2c var\(--value\)\] {
  background-color: var(--value), var(--value);
}
.bg-\[var\(--value1\)_var\(--value2\)\] {
  background-color: var(--value1) var(--value2);
}
.bg-opacity-\[0\.11\] {
  --tw-bg-opacity: 0.11;
}
.bg-opacity-\[var\(--value\)\] {
  --tw-bg-opacity: var(--value);
}
.bg-\[image\(\)\2c var\(--value\)\] {
  background-image: image(), var(--value);
}
.bg-\[image\:var\(--value\)\2c var\(--value\)\] {
  background-image: var(--value), var(--value);
}
.bg-\[linear-gradient\(\#eee\2c
  \#fff\)\2c
  conic-gradient\(red\2c
  orange\2c
  yellow\2c
  green\2c
  blue\)\] {
  background-image: linear-gradient(#eee, #fff), conic-gradient(red, orange, yellow, green, blue);
}
.bg-\[linear-gradient\(\#eee\2c \#fff\)\] {
  background-image: linear-gradient(#eee, #fff);
}
.bg-\[linear-gradient\(to_left\2c rgb\(var\(--green\)\)\2c blue\)\] {
  background-image: linear-gradient(to left, rgb(var(--green)), blue);
}
.bg-\[repeating-conic-gradient\(\#F8F9FA_0\%_25\%\2c _white_0\%_50\%\)\] {
  background-image: repeating-conic-gradient(#f8f9fa 0% 25%, white 0% 50%);
}
.bg-\[url\(\'\/path-to-image\.png\'\)\] {
  background-image: url('/path-to-image.png');
}
.bg-\[url\:var\(--url\)\] {
  background-image: var(--url);
}
.from-\[\#da5b66\] {
  --tw-gradient-from: #da5b66 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(218 91 102 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[var\(--color\)\] {
  --tw-gradient-from: var(--color) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-\[\#da5b66\] {
  --tw-gradient-to: rgb(218 91 102 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #da5b66 var(--tw-gradient-via-position),
    var(--tw-gradient-to);
}
.via-\[var\(--color\)\] {
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--color) var(--tw-gradient-via-position),
    var(--tw-gradient-to);
}
.to-\[\#da5b66\] {
  --tw-gradient-to: #da5b66 var(--tw-gradient-to-position);
}
.to-\[var\(--color\)\] {
  --tw-gradient-to: var(--color) var(--tw-gradient-to-position);
}
.bg-\[length\:200px_100px\] {
  background-size: 200px 100px;
}
.bg-\[length\:var\(--value\)\] {
  background-size: var(--value);
}
.bg-\[center_top_1rem\] {
  background-position: center top 1rem;
}
.bg-\[position\:200px_100px\] {
  background-position: 200px 100px;
}
.bg-\[position\:var\(--value\)\] {
  background-position: var(--value);
}
.fill-\[\#da5b66\] {
  fill: #da5b66;
}
.fill-\[url\(\#icon-gradient\)\] {
  fill: url(#icon-gradient);
}
.fill-\[var\(--value\)\] {
  fill: var(--value);
}
.stroke-\[\#da5b66\] {
  stroke: #da5b66;
}
.stroke-\[color\:var\(--value\)\] {
  stroke: var(--value);
}
.stroke-\[url\(\#icon-gradient\)\] {
  stroke: url(#icon-gradient);
}
.stroke-\[20px\] {
  stroke-width: 20px;
}
.stroke-\[length\:var\(--value\)\] {
  stroke-width: var(--value);
}
.object-\[50\%\2c 50\%\] {
  object-position: 50% 50%;
}
.object-\[top\2c right\] {
  object-position: top right;
}
.object-\[var\(--position\)\] {
  object-position: var(--position);
}
.p-\[7px\] {
  padding: 7px;
}
.px-\[7px\] {
  padding-left: 7px;
  padding-right: 7px;
}
.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}
.pb-\[7px\] {
  padding-bottom: 7px;
}
.pl-\[7px\] {
  padding-left: 7px;
}
.pr-\[7px\] {
  padding-right: 7px;
}
.pt-\[7px\] {
  padding-top: 7px;
}
.pt-\[clamp\(30px\2c 100px\)\] {
  padding-top: clamp(30px, 100px);
}
.indent-\[50\%\] {
  text-indent: 50%;
}
.indent-\[var\(--indent\)\] {
  text-indent: var(--indent);
}
.align-\[10em\] {
  vertical-align: 10em;
}
.font-\[\'Gill_Sans\'\] {
  font-family: 'Gill Sans';
}
.font-\[\'Some_Font\'\2c \'Some_Other_Font\'\] {
  font-family: 'Some Font', 'Some Other Font';
}
.font-\[\'Some_Font\'\2c sans-serif\] {
  font-family: 'Some Font', sans-serif;
}
.font-\[\'Some_Font\'\2c var\(--other-font\)\] {
  font-family: 'Some Font', var(--other-font);
}
.font-\[Georgia\2c serif\] {
  font-family: Georgia, serif;
}
.font-\[family-name\:var\(--value\)\] {
  font-family: var(--value);
}
.font-\[sans-serif\2c serif\] {
  font-family: sans-serif, serif;
}
.font-\[serif\2c var\(--value\)\] {
  font-family: serif, var(--value);
}
.text-\[0\] {
  font-size: 0;
}
.text-\[2\.23rem\] {
  font-size: 2.23rem;
}
.text-\[4cqw\] {
  font-size: 4cqw;
}
.text-\[length\:var\(--font-size\)\] {
  font-size: var(--font-size);
}
.text-\[min\(10vh\2c 100px\)\] {
  font-size: min(10vh, 100px);
}
.font-\[300\] {
  font-weight: 300;
}
.font-\[number\:lighter\] {
  font-weight: lighter;
}
.font-\[number\:var\(--value\)\] {
  font-weight: var(--value);
}
.leading-\[var\(--leading\)\] {
  line-height: var(--leading);
}
.tracking-\[var\(--tracking\)\] {
  letter-spacing: var(--tracking);
}
.text-\[black\] {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-\[color\:var\(--color\)\] {
  color: var(--color);
}
.text-\[rgb\(123\2c 123\2c 123\)\] {
  --tw-text-opacity: 1;
  color: rgb(123 123 123 / var(--tw-text-opacity, 1));
}
.text-\[rgb\(123\2c _123\2c _123\)\] {
  --tw-text-opacity: 1;
  color: rgb(123 123 123 / var(--tw-text-opacity, 1));
}
.text-\[rgb\(123_123_123\)\] {
  --tw-text-opacity: 1;
  color: rgb(123 123 123 / var(--tw-text-opacity, 1));
}
.text-opacity-\[0\.8\] {
  --tw-text-opacity: 0.8;
}
.text-opacity-\[var\(--value\)\] {
  --tw-text-opacity: var(--value);
}
.decoration-\[black\] {
  text-decoration-color: black;
}
.decoration-\[color\:var\(--color\)\] {
  text-decoration-color: var(--color);
}
.decoration-\[rgb\(123\2c 123\2c 123\)\] {
  text-decoration-color: rgb(123, 123, 123);
}
.decoration-\[rgb\(123\2c _123\2c _123\)\] {
  text-decoration-color: rgb(123, 123, 123);
}
.decoration-\[rgb\(123_123_123\)\] {
  text-decoration-color: rgb(123 123 123);
}
.decoration-\[length\:10px\] {
  text-decoration-thickness: 10px;
}
.underline-offset-\[10px\] {
  text-underline-offset: 10px;
}
.placeholder-\[var\(--placeholder\)\]::placeholder {
  color: var(--placeholder);
}
.placeholder-opacity-\[var\(--placeholder-opacity\)\]::placeholder {
  --tw-placeholder-opacity: var(--placeholder-opacity);
}
.caret-\[black\] {
  caret-color: black;
}
.caret-\[var\(--value\)\] {
  caret-color: var(--value);
}
.accent-\[\#bada55\] {
  accent-color: #bada55;
}
.accent-\[var\(--accent-color\)\] {
  accent-color: var(--accent-color);
}
.opacity-\[0\.8\] {
  opacity: 0.8;
}
.opacity-\[var\(--opacity\)\] {
  opacity: var(--opacity);
}
.shadow-\[0px_1px_2px_black\] {
  --tw-shadow: 0px 1px 2px black;
  --tw-shadow-colored: 0px 1px 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.shadow-\[shadow\:var\(--value\)\] {
  --tw-shadow: var(--value);
  --tw-shadow-colored: var(--value);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.outline-\[10px\] {
  outline-width: 10px;
}
.outline-\[length\:var\(--outline\)\] {
  outline-width: var(--outline);
}
.outline-offset-\[10px\] {
  outline-offset: 10px;
}
.outline-\[black\] {
  outline-color: black;
}
.outline-\[color\:var\(--outline\)\] {
  outline-color: var(--outline);
}
.ring-\[10px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(10px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-\[length\:\(var\(--value\)\)\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc((var(--value)) + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-\[\#76ad65\] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(118 173 101 / var(--tw-ring-opacity, 1));
}
.ring-\[color\:var\(--value\)\] {
  --tw-ring-color: var(--value);
}
.ring-opacity-\[var\(--ring-opacity\)\] {
  --tw-ring-opacity: var(--ring-opacity);
}
.ring-offset-\[19rem\] {
  --tw-ring-offset-width: 19rem;
}
.ring-offset-\[length\:var\(--value\)\] {
  --tw-ring-offset-width: var(--value);
}
.ring-offset-\[\#76ad65\] {
  --tw-ring-offset-color: #76ad65;
}
.ring-offset-\[\#ad672f\] {
  --tw-ring-offset-color: #ad672f;
}
.ring-offset-\[color\:var\(--value\)\] {
  --tw-ring-offset-color: var(--value);
}
.blur-\[15px\] {
  --tw-blur: blur(15px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.brightness-\[300\%\] {
  --tw-brightness: brightness(300%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.contrast-\[2\.4\] {
  --tw-contrast: contrast(2.4);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-\[0px_1px_2px_black\] {
  --tw-drop-shadow: drop-shadow(0px 1px 2px black);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale-\[0\.55\] {
  --tw-grayscale: grayscale(0.55);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.hue-rotate-\[0\.8turn\] {
  --tw-hue-rotate: hue-rotate(0.8turn);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.invert-\[0\.75\] {
  --tw-invert: invert(0.75);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.saturate-\[180\%\] {
  --tw-saturate: saturate(180%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.sepia-\[0\.2\] {
  --tw-sepia: sepia(0.2);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-\[11px\] {
  --tw-backdrop-blur: blur(11px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-brightness-\[1\.23\] {
  --tw-backdrop-brightness: brightness(1.23);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-contrast-\[0\.87\] {
  --tw-backdrop-contrast: contrast(0.87);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-grayscale-\[0\.42\] {
  --tw-backdrop-grayscale: grayscale(0.42);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-hue-rotate-\[1\.57rad\] {
  --tw-backdrop-hue-rotate: hue-rotate(1.57rad);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-invert-\[0\.66\] {
  --tw-backdrop-invert: invert(0.66);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-opacity-\[22\%\] {
  --tw-backdrop-opacity: opacity(22%);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-saturate-\[144\%\] {
  --tw-backdrop-saturate: saturate(144%);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-sepia-\[0\.38\] {
  --tw-backdrop-sepia: sepia(0.38);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition-\[opacity\2c width\] {
  transition-property: opacity, width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-\[var\(--delay\)\] {
  transition-delay: var(--delay);
}
.duration-\[2s\] {
  transition-duration: 2s;
}
.duration-\[var\(--app-duration\)\] {
  transition-duration: var(--app-duration);
}
.will-change-\[top\2c left\] {
  will-change: top, left;
}
.will-change-\[var\(--will-change\)\] {
  will-change: var(--will-change);
}
.content-\[\'\>\'\] {
  --tw-content: '>';
  content: var(--tw-content);
}
.content-\[\'hello\'\] {
  --tw-content: 'hello';
  content: var(--tw-content);
}
.content-\[attr\(content-before\)\] {
  --tw-content: attr(content-before);
  content: var(--tw-content);
}
@media (min-width: 1024px) {
  .lg\:grid-cols-\[200px\2c repeat\(auto-fill\2c minmax\(15\%\2c 100px\)\)\2c 300px\] {
    grid-template-columns: 200px repeat(auto-fill, minmax(15%, 100px)) 300px;
  }
}
