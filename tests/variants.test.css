*,
:before,
:after,
::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}
.first-letter\:text-2xl:first-letter {
  font-size: 1.5rem;
  line-height: 2rem;
}
.first-letter\:text-red-500:first-letter {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.first-line\:bg-yellow-300:first-line {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
}
.first-line\:underline:first-line {
  text-decoration-line: underline;
}
.marker\:text-lg ::marker {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.marker\:text-red-500 ::marker {
  color: #ef4444;
}
.marker\:text-lg::marker {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.marker\:text-red-500::marker {
  color: #ef4444;
}
.selection\:bg-blue-500 ::selection {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.selection\:text-white ::selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.selection\:bg-blue-500::selection {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.selection\:text-white::selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.file\:bg-blue-500::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.file\:text-white::file-selector-button {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.placeholder\:font-bold::placeholder {
  font-weight: 700;
}
.placeholder\:text-red-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.backdrop\:shadow-md::backdrop {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.before\:block:before {
  content: var(--tw-content);
  display: block;
}
.before\:bg-red-500:before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.after\:flex:after {
  content: var(--tw-content);
  display: flex;
}
.after\:uppercase:after {
  content: var(--tw-content);
  text-transform: uppercase;
}
.first\:shadow-md:first-child,
.last\:shadow-md:last-child,
.only\:shadow-md:only-child,
.odd\:shadow-md:nth-child(odd),
.even\:shadow-md:nth-child(2n),
.first-of-type\:shadow-md:first-of-type,
.last-of-type\:shadow-md:last-of-type,
.only-of-type\:shadow-md:only-of-type,
.visited\:shadow-md:visited,
.target\:shadow-md:target {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.open\:bg-red-200[open] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.default\:shadow-md:default,
.checked\:shadow-md:checked,
.indeterminate\:shadow-md:indeterminate,
.placeholder-shown\:shadow-md:placeholder-shown {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.autofill\:shadow-md:-webkit-autofill {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.autofill\:shadow-md:autofill {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.optional\:shadow-md:optional,
.required\:shadow-md:required,
.valid\:shadow-md:valid,
.invalid\:shadow-md:invalid,
.in-range\:shadow-md:in-range,
.out-of-range\:shadow-md:out-of-range,
.read-only\:shadow-md:read-only,
.empty\:shadow-md:empty,
.focus-within\:shadow-md:focus-within {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.hover\:animate-spin:hover {
  animation: 1s linear infinite spin;
}
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.file\:hover\:bg-blue-600:hover::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.open\:hover\:bg-red-200:hover[open] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.focus\:shadow-md:focus,
.focus\:hover\:shadow-md:hover:focus,
.focus-visible\:shadow-md:focus-visible,
.active\:shadow-md:active,
.enabled\:shadow-md:enabled,
.disabled\:shadow-md:disabled,
.group:first-child .group-first\:shadow-md,
.group:last-child .group-last\:shadow-md,
.group:only-child .group-only\:shadow-md,
.group:nth-child(odd) .group-odd\:shadow-md,
.group:nth-child(2n) .group-even\:shadow-md,
.group:first-of-type .group-first-of-type\:shadow-md,
.group:last-of-type .group-last-of-type\:shadow-md,
.group:only-of-type .group-only-of-type\:shadow-md,
.group:visited .group-visited\:shadow-md,
.group:target .group-target\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.group[open] .group-open\:bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.group:default .group-default\:shadow-md,
.group:checked .group-checked\:shadow-md,
.group:indeterminate .group-indeterminate\:shadow-md,
.group:placeholder-shown .group-placeholder-shown\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.group:-webkit-autofill .group-autofill\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.group:autofill .group-autofill\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.group:optional .group-optional\:shadow-md,
.group:required .group-required\:shadow-md,
.group:valid .group-valid\:shadow-md,
.group:invalid .group-invalid\:shadow-md,
.group:in-range .group-in-range\:shadow-md,
.group:out-of-range .group-out-of-range\:shadow-md,
.group:read-only .group-read-only\:shadow-md,
.group:empty .group-empty\:shadow-md,
.group:focus-within .group-focus-within\:shadow-md,
.group:hover .group-hover\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.group[open]:hover .group-open\:group-hover\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.group:focus .group-focus\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.group[open]:focus .group-open\:group-focus\:bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.group:focus:hover .group-focus\:group-hover\:shadow-md,
.group:focus-visible .group-focus-visible\:shadow-md,
.group:active .group-active\:shadow-md,
.group:enabled .group-enabled\:shadow-md,
.group:disabled .group-disabled\:shadow-md,
.group:disabled:focus:hover .group-disabled\:group-focus\:group-hover\:shadow-md,
.group:disabled:focus:hover .group-disabled\:group-focus\:group-hover\:first\:shadow-md:first-child,
.peer:first-child ~ .peer-first\:shadow-md,
.peer:last-child ~ .peer-last\:shadow-md,
.peer:only-child ~ .peer-only\:shadow-md,
.peer:nth-child(odd) ~ .peer-odd\:shadow-md,
.peer:nth-child(2n) ~ .peer-even\:shadow-md,
.peer:first-of-type ~ .peer-first-of-type\:shadow-md,
.peer:last-of-type ~ .peer-last-of-type\:shadow-md,
.peer:only-of-type ~ .peer-only-of-type\:shadow-md,
.peer:visited ~ .peer-visited\:shadow-md,
.peer:target ~ .peer-target\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.peer[open] ~ .peer-open\:bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.peer:default ~ .peer-default\:shadow-md,
.peer:checked ~ .peer-checked\:shadow-md,
.peer:indeterminate ~ .peer-indeterminate\:shadow-md,
.peer:placeholder-shown ~ .peer-placeholder-shown\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.peer:-webkit-autofill ~ .peer-autofill\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.peer:autofill ~ .peer-autofill\:shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.peer:optional ~ .peer-optional\:shadow-md,
.peer:required ~ .peer-required\:shadow-md,
.peer:valid ~ .peer-valid\:shadow-md,
.peer:invalid ~ .peer-invalid\:shadow-md,
.peer:in-range ~ .peer-in-range\:shadow-md,
.peer:out-of-range ~ .peer-out-of-range\:shadow-md,
.peer:read-only ~ .peer-read-only\:shadow-md,
.peer:empty ~ .peer-empty\:shadow-md,
.peer:focus-within ~ .peer-focus-within\:shadow-md,
.peer:hover ~ .peer-hover\:shadow-md,
.peer:focus ~ .peer-focus\:shadow-md,
.peer:focus:hover ~ .peer-focus\:peer-hover\:shadow-md,
.peer:focus-visible ~ .peer-focus-visible\:shadow-md,
.peer:active ~ .peer-active\:shadow-md,
.peer:enabled ~ .peer-enabled\:shadow-md,
.peer:disabled ~ .peer-disabled\:shadow-md,
.peer:disabled:focus:hover ~ .peer-disabled\:peer-focus\:peer-hover\:shadow-md,
.peer:disabled:focus:hover ~ .peer-disabled\:peer-focus\:peer-hover\:first\:shadow-md:first-child {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
@media (prefers-reduced-motion: no-preference) {
  .motion-safe\:shadow-md {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:shadow-md {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (prefers-contrast: more) {
  .contrast-more\:bg-yellow-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
  }
}
@media (prefers-contrast: less) {
  .contrast-less\:bg-yellow-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
  }
}
@media (width >= 640px) {
  .sm\:shadow-md,
  .sm\:active\:shadow-md:active {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (width >= 768px) {
  .md\:shadow-md,
  .group:focus .md\:group-focus\:shadow-md {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (width >= 1024px) {
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  .lg\:animate-spin {
    animation: 1s linear infinite spin;
  }
  .lg\:shadow-md {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (width >= 1280px) {
  .xl\:shadow-md {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (width >= 1536px) {
  .\32 xl\:shadow-md {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (orientation: portrait) {
  .portrait\:bg-yellow-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
  }
}
@media (orientation: landscape) {
  .landscape\:bg-yellow-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
  }
}
.ltr\:shadow-md:where([dir='ltr'], [dir='ltr'] *),
.rtl\:shadow-md:where([dir='rtl'], [dir='rtl'] *),
.dark\:shadow-md:where(.dark, .dark *),
.group:disabled:focus:hover
  .dark\:group-disabled\:group-focus\:group-hover\:shadow-md:where(.dark, .dark *),
.peer:disabled:focus:hover
  ~ .dark\:peer-disabled\:peer-focus\:peer-hover\:shadow-md:where(.dark, .dark *) {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
@media (width >= 1024px) {
  .lg\:dark\:shadow-md:where(.dark, .dark *) {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (width >= 1280px) {
  .xl\:dark\:disabled\:shadow-md:disabled:where(.dark, .dark *) {
    --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
      0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
@media (width >= 1536px) {
  @media (prefers-reduced-motion: no-preference) {
    .\32 xl\:dark\:motion-safe\:focus-within\:shadow-md:focus-within:where(.dark, .dark *) {
      --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
      --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
        0 2px 4px -2px var(--tw-shadow-color);
      box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
        var(--tw-shadow);
    }
  }
}
@media (forced-colors: active) {
  .forced-colors\:flex {
    display: flex;
  }
}
@media print {
  .print\:bg-yellow-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
  }
}
