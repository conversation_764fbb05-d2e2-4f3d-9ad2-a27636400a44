<!-- Basic pseudo variants -->
<div class="first:shadow-md"></div>
<div class="last:shadow-md"></div>
<div class="only:shadow-md"></div>
<div class="even:shadow-md"></div>
<div class="odd:shadow-md"></div>
<div class="first-of-type:shadow-md"></div>
<div class="last-of-type:shadow-md"></div>
<div class="only-of-type:shadow-md"></div>
<div class="hover:shadow-md"></div>
<div class="focus:shadow-md"></div>
<div class="enabled:shadow-md"></div>
<div class="disabled:shadow-md"></div>
<div class="active:shadow-md"></div>
<div class="target:shadow-md"></div>
<div class="open:bg-red-200"></div>
<div class="visited:shadow-md"></div>
<div class="default:shadow-md"></div>
<div class="checked:shadow-md"></div>
<div class="indeterminate:shadow-md"></div>
<div class="placeholder-shown:shadow-md"></div>
<div class="autofill:shadow-md"></div>
<div class="focus-within:shadow-md"></div>
<div class="focus-visible:shadow-md"></div>
<div class="optional:shadow-md"></div>
<div class="required:shadow-md"></div>
<div class="valid:shadow-md"></div>
<div class="invalid:shadow-md"></div>
<div class="in-range:shadow-md"></div>
<div class="out-of-range:shadow-md"></div>
<div class="read-only:shadow-md"></div>
<div class="empty:shadow-md"></div>

<!-- Pseudo-element variants -->
<div class="first-letter:text-2xl first-letter:text-red-500"></div>
<div class="first-line:bg-yellow-300 first-line:underline"></div>
<ul>
  <li class="marker:text-lg marker:text-red-500"></li>
</ul>
<div class="selection:bg-blue-500 selection:text-white"></div>
<div class="file:bg-blue-500 file:text-white"></div>
<div class="before:block before:bg-red-500"></div>
<div class="after:flex after:uppercase"></div>
<div class="placeholder:font-bold placeholder:text-red-500"></div>
<dialog class="backdrop:shadow-md"></dialog>

<!-- Group variants -->
<div class="group-open:bg-red-200"></div>
<div class="group-first:shadow-md"></div>
<div class="group-last:shadow-md"></div>
<div class="group-only:shadow-md"></div>
<div class="group-even:shadow-md"></div>
<div class="group-odd:shadow-md"></div>
<div class="group-first-of-type:shadow-md"></div>
<div class="group-last-of-type:shadow-md"></div>
<div class="group-only-of-type:shadow-md"></div>
<div class="group-hover:shadow-md"></div>
<div class="group-focus:shadow-md"></div>
<div class="group-enabled:shadow-md"></div>
<div class="group-disabled:shadow-md"></div>
<div class="group-active:shadow-md"></div>
<div class="group-target:shadow-md"></div>
<div class="group-visited:shadow-md"></div>
<div class="group-default:shadow-md"></div>
<div class="group-checked:shadow-md"></div>
<div class="group-indeterminate:shadow-md"></div>
<div class="group-placeholder-shown:shadow-md"></div>
<div class="group-autofill:shadow-md"></div>
<div class="group-focus-within:shadow-md"></div>
<div class="group-focus-visible:shadow-md"></div>
<div class="group-optional:shadow-md"></div>
<div class="group-required:shadow-md"></div>
<div class="group-valid:shadow-md"></div>
<div class="group-invalid:shadow-md"></div>
<div class="group-in-range:shadow-md"></div>
<div class="group-out-of-range:shadow-md"></div>
<div class="group-read-only:shadow-md"></div>
<div class="group-empty:shadow-md"></div>

<!-- Peer variants -->
<div class="peer-open:bg-red-200"></div>
<div class="peer-first:shadow-md"></div>
<div class="peer-last:shadow-md"></div>
<div class="peer-only:shadow-md"></div>
<div class="peer-even:shadow-md"></div>
<div class="peer-odd:shadow-md"></div>
<div class="peer-first-of-type:shadow-md"></div>
<div class="peer-last-of-type:shadow-md"></div>
<div class="peer-only-of-type:shadow-md"></div>
<div class="peer-hover:shadow-md"></div>
<div class="peer-focus:shadow-md"></div>
<div class="peer-enabled:shadow-md"></div>
<div class="peer-disabled:shadow-md"></div>
<div class="peer-active:shadow-md"></div>
<div class="peer-target:shadow-md"></div>
<div class="peer-visited:shadow-md"></div>
<div class="peer-default:shadow-md"></div>
<div class="peer-checked:shadow-md"></div>
<div class="peer-indeterminate:shadow-md"></div>
<div class="peer-placeholder-shown:shadow-md"></div>
<div class="peer-autofill:shadow-md"></div>
<div class="peer-focus-within:shadow-md"></div>
<div class="peer-focus-visible:shadow-md"></div>
<div class="peer-optional:shadow-md"></div>
<div class="peer-required:shadow-md"></div>
<div class="peer-valid:shadow-md"></div>
<div class="peer-invalid:shadow-md"></div>
<div class="peer-in-range:shadow-md"></div>
<div class="peer-out-of-range:shadow-md"></div>
<div class="peer-read-only:shadow-md"></div>
<div class="peer-empty:shadow-md"></div>

<!-- Reduced motion variants -->
<div class="motion-safe:shadow-md"></div>
<div class="motion-reduce:shadow-md"></div>

<!-- Direction variants -->
<div class="rtl:shadow-md"></div>
<div class="ltr:shadow-md"></div>

<!-- Dark mode variants -->
<div class="dark:shadow-md"></div>

<!-- Forced colors variant -->
<div class="forced-colors:flex"></div>

<!-- Print variant -->
<div class="print:bg-yellow-300"></div>

<!-- Screen variants -->
<div class="sm:shadow-md"></div>
<div class="md:shadow-md"></div>
<div class="lg:shadow-md"></div>
<div class="xl:shadow-md"></div>
<div class="2xl:shadow-md"></div>
<div class="lg:animate-spin"></div>
<div class="hover:animate-spin"></div>

<!-- Orientation variants -->
<div class="portrait:bg-yellow-300"></div>
<div class="landscape:bg-yellow-300"></div>

<!-- Prefers contrast variants -->
<div class="contrast-more:bg-yellow-300"></div>
<div class="contrast-less:bg-yellow-300"></div>

<!-- Stacked variants -->
<div class="open:hover:bg-red-200"></div>
<div class="file:hover:bg-blue-600"></div>
<div class="focus:hover:shadow-md"></div>
<div class="sm:active:shadow-md"></div>
<div class="md:group-focus:shadow-md"></div>
<div class="lg:dark:shadow-md"></div>
<div class="xl:dark:disabled:shadow-md"></div>
<div class="2xl:dark:motion-safe:focus-within:shadow-md"></div>

<!-- Stacked group variants -->
<div class="group-open:group-focus:bg-red-200"></div>
<div class="group-open:group-hover:space-x-2"></div>
<div class="group-focus:group-hover:shadow-md"></div>
<div class="group-disabled:group-focus:group-hover:shadow-md"></div>
<div class="dark:group-disabled:group-focus:group-hover:shadow-md"></div>
<div class="group-disabled:group-focus:group-hover:first:shadow-md"></div>

<!-- Stacked peer variants -->
<div class="peer-focus:peer-hover:shadow-md"></div>
<div class="peer-disabled:peer-focus:peer-hover:shadow-md"></div>
<div class="dark:peer-disabled:peer-focus:peer-hover:shadow-md"></div>
<div class="peer-disabled:peer-focus:peer-hover:first:shadow-md"></div>
