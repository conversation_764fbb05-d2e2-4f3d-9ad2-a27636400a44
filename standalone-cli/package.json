{"name": "tailwindcss-standalone", "version": "0.0.0", "repository": {"type": "git", "url": "git+https://github.com/tailwindlabs/tailwindcss.git", "directory": "standalone-cli"}, "bin": "standalone.js", "scripts": {"build": "pkg . --compress Brotli --no-bytecode --public-packages \"*\" --public", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "move-file dist/tailwindcss-standalone-macos-x64 dist/tailwindcss-macos-x64 && move-file dist/tailwindcss-standalone-macos-arm64 dist/tailwindcss-macos-arm64 && move-file dist/tailwindcss-standalone-win-x64.exe dist/tailwindcss-windows-x64.exe && move-file dist/tailwindcss-standalone-win-arm64.exe dist/tailwindcss-windows-arm64.exe && move-file dist/tailwindcss-standalone-linuxstatic-x64 dist/tailwindcss-linux-x64 && move-file dist/tailwindcss-standalone-linuxstatic-arm64 dist/tailwindcss-linux-arm64 && move-file dist/tailwindcss-standalone-linuxstatic-armv7 dist/tailwindcss-linux-armv7 && npm run generate-checksums", "generate-checksums": "node ./scripts/checksum.mjs", "test": "jest"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "fs-extra": "^10.1.0", "jest": "^27.5.1", "move-file-cli": "^3.0.0", "pkg": "^5.8.1", "rimraf": "^3.0.2", "tailwindcss": "file:.."}, "pkg": {"assets": "node_modules/tailwindcss/lib/css/preflight.css", "targets": ["node16-macos-x64", "node16-macos-arm64", "node16-win-x64", "node16-win-arm64", "node16-linuxstatic-x64", "node16-linuxstatic-arm64", "node16-linuxstatic-armv7"], "outputPath": "dist"}, "jest": {"testTimeout": 30000}}