/**
 * CSS to Tailwind CSS Converter
 * 基于 Tailwind CSS 源码实现的反向转换器
 */

class CssToTailwindConverter {
  constructor() {
    this.initializeMappings()
  }

  initializeMappings() {
    // 基于 Tailwind 默认主题的间距映射
    this.spacingMap = {
      '0px': '0',
      '1px': 'px',
      '0.125rem': '0.5',
      '0.25rem': '1',
      '0.375rem': '1.5',
      '0.5rem': '2',
      '0.625rem': '2.5',
      '0.75rem': '3',
      '0.875rem': '3.5',
      '1rem': '4',
      '1.25rem': '5',
      '1.5rem': '6',
      '1.75rem': '7',
      '2rem': '8',
      '2.25rem': '9',
      '2.5rem': '10',
      '2.75rem': '11',
      '3rem': '12',
      '3.5rem': '14',
      '4rem': '16',
      '5rem': '20',
      '6rem': '24',
      '8rem': '32',
      '10rem': '40',
      '12rem': '48',
      '14rem': '56',
      '16rem': '64',
      '20rem': '80',
      '24rem': '96',
    }

    // 颜色映射 (简化版本)
    this.colorMap = {
      '#000000': 'black',
      '#ffffff': 'white',
      '#f9fafb': 'gray-50',
      '#f3f4f6': 'gray-100',
      '#e5e7eb': 'gray-200',
      '#d1d5db': 'gray-300',
      '#9ca3af': 'gray-400',
      '#6b7280': 'gray-500',
      '#374151': 'gray-600',
      '#1f2937': 'gray-700',
      '#111827': 'gray-800',
      '#0f172a': 'gray-900',
      '#ef4444': 'red-500',
      '#3b82f6': 'blue-500',
      '#10b981': 'green-500',
      '#f59e0b': 'yellow-500',
      '#8b5cf6': 'purple-500',
      '#ec4899': 'pink-500',
    }

    // CSS 属性到 Tailwind 类名前缀的映射
    this.propertyMap = {
      // Spacing
      padding: 'p',
      'padding-top': 'pt',
      'padding-right': 'pr',
      'padding-bottom': 'pb',
      'padding-left': 'pl',
      margin: 'm',
      'margin-top': 'mt',
      'margin-right': 'mr',
      'margin-bottom': 'mb',
      'margin-left': 'ml',

      // Layout
      display: '',
      position: '',
      top: 'top',
      right: 'right',
      bottom: 'bottom',
      left: 'left',
      'z-index': 'z',

      // Flexbox
      'flex-direction': '',
      'flex-wrap': '',
      'align-items': '',
      'justify-content': '',
      flex: '',
      'flex-grow': 'grow',
      'flex-shrink': 'shrink',

      // Sizing
      width: 'w',
      height: 'h',
      'min-width': 'min-w',
      'min-height': 'min-h',
      'max-width': 'max-w',
      'max-height': 'max-h',

      // Typography
      'font-size': 'text',
      'font-weight': 'font',
      'line-height': 'leading',
      'text-align': 'text',
      color: 'text',
      'text-decoration': '',

      // Background
      'background-color': 'bg',
      'background-image': 'bg',
      'background-size': 'bg',
      'background-position': 'bg',
      'background-repeat': 'bg',

      // Border
      'border-width': 'border',
      'border-color': 'border',
      'border-radius': 'rounded',
      'border-style': 'border',

      // Effects
      'box-shadow': 'shadow',
      opacity: 'opacity',
    }

    // 特殊值映射
    this.specialValues = {
      display: {
        block: 'block',
        'inline-block': 'inline-block',
        inline: 'inline',
        flex: 'flex',
        'inline-flex': 'inline-flex',
        grid: 'grid',
        hidden: 'hidden',
        none: 'hidden',
      },
      position: {
        static: 'static',
        fixed: 'fixed',
        absolute: 'absolute',
        relative: 'relative',
        sticky: 'sticky',
      },
      'text-align': {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
        justify: 'text-justify',
      },
      'flex-direction': {
        row: 'flex-row',
        'row-reverse': 'flex-row-reverse',
        column: 'flex-col',
        'column-reverse': 'flex-col-reverse',
      },
      'align-items': {
        'flex-start': 'items-start',
        center: 'items-center',
        'flex-end': 'items-end',
        stretch: 'items-stretch',
        baseline: 'items-baseline',
      },
      'justify-content': {
        'flex-start': 'justify-start',
        center: 'justify-center',
        'flex-end': 'justify-end',
        'space-between': 'justify-between',
        'space-around': 'justify-around',
        'space-evenly': 'justify-evenly',
      },
      'font-weight': {
        '100': 'font-thin',
        '200': 'font-extralight',
        '300': 'font-light',
        '400': 'font-normal',
        '500': 'font-medium',
        '600': 'font-semibold',
        '700': 'font-bold',
        '800': 'font-extrabold',
        '900': 'font-black',
      },
    }
  }

  /**
   * 将 CSS 声明转换为 Tailwind 类名
   * @param {string} cssText - CSS 文本，如 "padding: 1rem; color: #3b82f6;"
   * @returns {string[]} - Tailwind 类名数组
   */
  convertCssToTailwind(cssText) {
    const declarations = this.parseCssDeclarations(cssText)
    const tailwindClasses = []

    for (const { property, value } of declarations) {
      const classes = this.convertProperty(property, value)
      tailwindClasses.push(...classes)
    }

    return tailwindClasses.filter(Boolean)
  }

  /**
   * 解析 CSS 声明
   */
  parseCssDeclarations(cssText) {
    const declarations = []
    const rules = cssText.split(';').filter((rule) => rule.trim())

    for (const rule of rules) {
      const [property, value] = rule.split(':').map((s) => s.trim())
      if (property && value) {
        declarations.push({ property, value })
      }
    }

    return declarations
  }

  /**
   * 转换单个 CSS 属性
   */
  convertProperty(property, value) {
    // 处理特殊值映射
    if (this.specialValues[property]) {
      const specialValue = this.specialValues[property][value]
      if (specialValue) {
        return [specialValue]
      }
    }

    // 获取属性前缀
    const prefix = this.propertyMap[property]
    if (!prefix && prefix !== '') {
      return [this.generateArbitraryProperty(property, value)]
    }

    // 处理复合属性
    if (this.isCompoundProperty(property, value)) {
      return this.handleCompoundProperty(property, value)
    }

    // 处理颜色值
    if (this.isColorProperty(property)) {
      return this.handleColorProperty(prefix, value)
    }

    // 处理间距值
    if (this.isSpacingProperty(property)) {
      return this.handleSpacingProperty(prefix, value)
    }

    // 处理其他数值
    const tailwindValue = this.convertValue(value, property)
    if (prefix === '') {
      return [tailwindValue]
    }

    return [`${prefix}-${tailwindValue}`]
  }

  /**
   * 检查是否为复合属性
   */
  isCompoundProperty(property, value) {
    const compoundProperties = ['margin', 'padding', 'border-width']
    return compoundProperties.includes(property) && value.split(' ').length > 1
  }

  /**
   * 处理复合属性
   */
  handleCompoundProperty(property, value) {
    const values = value.split(' ').filter((v) => v.trim())
    const classes = []

    if (property === 'margin' || property === 'padding') {
      const prefix = property === 'margin' ? 'm' : 'p'

      if (values.length === 1) {
        classes.push(`${prefix}-${this.convertSpacingValue(values[0])}`)
      } else if (values.length === 2) {
        classes.push(`${prefix}y-${this.convertSpacingValue(values[0])}`)
        classes.push(`${prefix}x-${this.convertSpacingValue(values[1])}`)
      } else if (values.length === 4) {
        classes.push(`${prefix}t-${this.convertSpacingValue(values[0])}`)
        classes.push(`${prefix}r-${this.convertSpacingValue(values[1])}`)
        classes.push(`${prefix}b-${this.convertSpacingValue(values[2])}`)
        classes.push(`${prefix}l-${this.convertSpacingValue(values[3])}`)
      }
    }

    return classes
  }

  /**
   * 检查是否为颜色属性
   */
  isColorProperty(property) {
    return ['color', 'background-color', 'border-color'].includes(property)
  }

  /**
   * 处理颜色属性
   */
  handleColorProperty(prefix, value) {
    const normalizedColor = this.normalizeColor(value)
    const tailwindColor = this.colorMap[normalizedColor]

    if (tailwindColor) {
      return [`${prefix}-${tailwindColor}`]
    }

    return [`${prefix}-[${value}]`]
  }

  /**
   * 检查是否为间距属性
   */
  isSpacingProperty(property) {
    return (
      property.includes('padding') ||
      property.includes('margin') ||
      ['top', 'right', 'bottom', 'left'].includes(property)
    )
  }

  /**
   * 处理间距属性
   */
  handleSpacingProperty(prefix, value) {
    const tailwindValue = this.convertSpacingValue(value)
    return [`${prefix}-${tailwindValue}`]
  }

  /**
   * 转换间距值
   */
  convertSpacingValue(value) {
    // 处理负值
    if (value.startsWith('-')) {
      const positiveValue = value.substring(1)
      const converted = this.spacingMap[positiveValue]
      return converted ? `-${converted}` : `[-${positiveValue}]`
    }

    return this.spacingMap[value] || `[${value}]`
  }

  /**
   * 转换通用值
   */
  convertValue(value, _property) {
    // 处理百分比
    if (value.endsWith('%')) {
      const num = parseFloat(value)
      if (num === 50) return '1/2'
      if (num === 33.333333 || num === 33.33) return '1/3'
      if (num === 66.666667 || num === 66.67) return '2/3'
      if (num === 25) return '1/4'
      if (num === 75) return '3/4'
      if (num === 100) return 'full'
    }

    // 处理 auto
    if (value === 'auto') return 'auto'

    // 处理数字
    if (!isNaN(value)) return value

    // 返回任意值
    return `[${value}]`
  }

  /**
   * 标准化颜色值
   */
  normalizeColor(color) {
    // 转换 rgb 到 hex
    if (color.startsWith('rgb')) {
      return this.rgbToHex(color)
    }

    // 标准化 hex 颜色
    if (color.startsWith('#')) {
      return color.toLowerCase()
    }

    return color
  }

  /**
   * RGB 转 HEX
   */
  rgbToHex(rgb) {
    const match = rgb.match(/\d+/g)
    if (match && match.length >= 3) {
      const r = parseInt(match[0])
      const g = parseInt(match[1])
      const b = parseInt(match[2])
      return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
    }
    return rgb
  }

  /**
   * 生成任意属性
   */
  generateArbitraryProperty(property, value) {
    return `[${property}:${value}]`
  }

  /**
   * 批量转换 CSS 对象
   * @param {Object} cssObject - CSS 对象，如 { padding: '1rem', color: '#3b82f6' }
   * @returns {string[]} - Tailwind 类名数组
   */
  convertCssObject(cssObject) {
    const tailwindClasses = []

    for (const [property, value] of Object.entries(cssObject)) {
      const classes = this.convertProperty(property, value)
      tailwindClasses.push(...classes)
    }

    return tailwindClasses.filter(Boolean)
  }

  /**
   * 转换 CSS 规则字符串（包含选择器）
   * @param {string} cssRule - 完整的 CSS 规则，如 ".button { padding: 1rem; color: blue; }"
   * @returns {Object} - { selector: string, classes: string[] }
   */
  convertCssRule(cssRule) {
    const match = cssRule.match(/([^{]+)\s*\{([^}]+)\}/)
    if (!match) {
      throw new Error('Invalid CSS rule format')
    }

    const selector = match[1].trim()
    const declarations = match[2].trim()
    const classes = this.convertCssToTailwind(declarations)

    return { selector, classes }
  }

  /**
   * 优化类名列表，移除冲突和重复
   * @param {string[]} classes - Tailwind 类名数组
   * @returns {string[]} - 优化后的类名数组
   */
  optimizeClasses(classes) {
    const optimized = []
    const seen = new Set()
    const conflicts = new Map()

    // 定义冲突组
    const conflictGroups = [
      ['p-', 'pt-', 'pr-', 'pb-', 'pl-', 'px-', 'py-'],
      ['m-', 'mt-', 'mr-', 'mb-', 'ml-', 'mx-', 'my-'],
      ['w-', 'min-w-', 'max-w-'],
      ['h-', 'min-h-', 'max-h-'],
      ['text-left', 'text-center', 'text-right', 'text-justify'],
      ['flex', 'block', 'inline', 'inline-block', 'grid', 'hidden'],
    ]

    for (const className of classes) {
      if (seen.has(className)) continue

      // 检查冲突
      let hasConflict = false
      for (const group of conflictGroups) {
        const matchingPrefixes = group.filter(
          (prefix) => className.startsWith(prefix) || group.includes(className)
        )

        if (matchingPrefixes.length > 0) {
          const key = group.join('|')
          if (conflicts.has(key)) {
            hasConflict = true
            break
          }
          conflicts.set(key, className)
        }
      }

      if (!hasConflict) {
        optimized.push(className)
        seen.add(className)
      }
    }

    return optimized
  }

  /**
   * 获取建议的替代类名
   * @param {string} property - CSS 属性
   * @param {string} value - CSS 值
   * @returns {string[]} - 建议的类名数组
   */
  getSuggestions(property, value) {
    const suggestions = []

    // 基于属性类型提供建议
    if (this.isSpacingProperty(property)) {
      const spacingValues = Object.keys(this.spacingMap)
      const closest = this.findClosestValue(value, spacingValues)
      if (closest && closest !== value) {
        const prefix = this.propertyMap[property]
        const tailwindValue = this.spacingMap[closest]
        suggestions.push(`${prefix}-${tailwindValue}`)
      }
    }

    if (this.isColorProperty(property)) {
      const closest = this.findClosestColor(value)
      if (closest) {
        const prefix = this.propertyMap[property]
        suggestions.push(`${prefix}-${closest}`)
      }
    }

    return suggestions
  }

  /**
   * 查找最接近的值
   */
  findClosestValue(target, values) {
    // 简化的值匹配逻辑
    const targetNum = parseFloat(target)
    if (isNaN(targetNum)) return null

    let closest = null
    let minDiff = Infinity

    for (const value of values) {
      const num = parseFloat(value)
      if (!isNaN(num)) {
        const diff = Math.abs(targetNum - num)
        if (diff < minDiff) {
          minDiff = diff
          closest = value
        }
      }
    }

    return closest
  }

  /**
   * 查找最接近的颜色
   */
  findClosestColor(_targetColor) {
    // 这里可以实现更复杂的颜色匹配算法
    // 目前返回 null，使用任意值
    return null
  }

  /**
   * 验证生成的类名是否有效
   * @param {string[]} classes - Tailwind 类名数组
   * @returns {Object} - { valid: string[], invalid: string[] }
   */
  validateClasses(classes) {
    const valid = []
    const invalid = []

    for (const className of classes) {
      if (this.isValidTailwindClass(className)) {
        valid.push(className)
      } else {
        invalid.push(className)
      }
    }

    return { valid, invalid }
  }

  /**
   * 检查是否为有效的 Tailwind 类名
   */
  isValidTailwindClass(className) {
    // 基础验证逻辑
    if (!className || typeof className !== 'string') return false

    // 检查任意值语法
    if (className.startsWith('[') && className.endsWith(']')) {
      return true
    }

    // 检查负值
    if (className.startsWith('-')) {
      return this.isValidTailwindClass(className.substring(1))
    }

    // 检查已知的类名模式
    const knownPrefixes = Object.values(this.propertyMap).filter(Boolean)
    const specialClasses = Object.values(this.specialValues).flatMap(Object.values)

    return (
      knownPrefixes.some((prefix) => className.startsWith(prefix + '-')) ||
      specialClasses.includes(className)
    )
  }
}

// 导出转换器
export default CssToTailwindConverter

// 使用示例
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CssToTailwindConverter
}
