{"name": "@tailwindcss/integrations-vite", "private": true, "version": "0.0.0", "main": "./src/index.js", "browser": "./src/index.js", "scripts": {"build": "vite build", "test": "jest --runInBand --forceExit"}, "jest": {"testTimeout": 10000, "displayName": "vite", "setupFilesAfterEnv": ["<rootDir>/../../jest/customMatchers.js"], "transform": {"\\.js$": "@swc/jest", "\\.ts$": "@swc/jest"}}, "devDependencies": {"isomorphic-fetch": "^3.0.0", "vite": "^5.1.6"}}