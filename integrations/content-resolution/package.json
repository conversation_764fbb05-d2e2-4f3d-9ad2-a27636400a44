{"name": "@tailwindcss/integrations-content-resolution", "private": true, "version": "0.0.0", "scripts": {"build": "NODE_ENV=production postcss ./src/index.css -o ./dist/main.css --verbose", "test": "jest --runInBand --forceExit"}, "jest": {"testTimeout": 10000, "displayName": "Content Resolution", "setupFilesAfterEnv": ["<rootDir>/../../jest/customMatchers.js"], "transform": {"\\.js$": "@swc/jest", "\\.ts$": "@swc/jest"}}, "devDependencies": {"postcss": "^8.4.23", "postcss-cli": "^10.1.0"}}