{"name": "@tailwindcss/integrations-tailwindcss-cli", "private": true, "version": "0.0.0", "scripts": {"build": "NODE_ENV=production node ../../lib/cli.js -i ./src/index.css -o ./dist/main.css", "test": "jest --runInBand --forceExit"}, "dependencies": {"tailwindcss": "file:../../"}, "jest": {"testTimeout": 10000, "displayName": "Tailwind CSS CLI", "setupFilesAfterEnv": ["<rootDir>/../../jest/customMatchers.js"], "transform": {"\\.js$": "@swc/jest", "\\.ts$": "@swc/jest"}}}