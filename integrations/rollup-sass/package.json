{"name": "@tailwindcss/integrations-rollup-sass", "private": true, "version": "0.0.0", "scripts": {"build": "rollup -c", "test": "jest --runInBand --forceExit"}, "jest": {"testTimeout": 10000, "displayName": "rollup.js", "setupFilesAfterEnv": ["<rootDir>/../../jest/customMatchers.js"], "transform": {"\\.js$": "@swc/jest", "\\.ts$": "@swc/jest"}}, "devDependencies": {"rollup": "^4.13.0", "rollup-plugin-postcss": "^4.0.2", "sass": "^1.72.0"}}