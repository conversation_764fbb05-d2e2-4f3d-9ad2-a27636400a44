{"name": "@tailwindcss/integrations-webpack-4", "private": true, "version": "0.0.0", "main": "./src/index.js", "browser": "./src/index.js", "scripts": {"build": "webpack --mode=production", "dev": "webpack --mode=development --watch", "test": "NODE_OPTIONS=--openssl-legacy-provider jest --runInBand --forceExit"}, "jest": {"testTimeout": 10000, "displayName": "webpack 4", "setupFilesAfterEnv": ["<rootDir>/../../jest/customMatchers.js"], "transform": {"\\.js$": "@swc/jest", "\\.ts$": "@swc/jest"}}, "devDependencies": {"css-loader": "^5.2.7", "mini-css-extract-plugin": "^1.6.2", "postcss-loader": "^4.3.0", "webpack": "^4.46.0", "webpack-cli": "^4.9.2"}}