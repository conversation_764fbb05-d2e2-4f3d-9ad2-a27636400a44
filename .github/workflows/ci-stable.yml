# The only reason this file exists is so that the `badge` in the README of the
# currently published version isn't broken (https://www.npmjs.com/package/tailwindcss).
#
# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: CI — Stable

on:
  push:
    branches: [main]
  pull_request:
    branches: [main, 3.3, 3.4]

permissions:
  contents: read

env:
  CI: true
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
  CACHE_PREFIX: stable

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14, 18]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Cache node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-${{ matrix.node-version }}-${{ env.CACHE_PREFIX }}-node_modules-${{ hashFiles('**/package-lock.json') }}

      - name: Install dependencies
        run: npm install

      - name: Build Tailwind CSS
        run: npm run build

      - name: Test
        run: |
          npm run test || \
          npm run test || \
          npm run test || exit 1

      - name: Lint
        run: npm run style
