<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS to Tailwind Converter Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .code-block {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .highlight {
            background-color: #fef3c7;
            padding: 2px 4px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">CSS to Tailwind Converter</h1>
            <p class="text-lg text-gray-600">将 CSS 样式转换为 Tailwind CSS 原子类</p>
        </header>

        <div class="max-w-4xl mx-auto">
            <!-- 输入区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">输入 CSS 样式</h2>
                <textarea 
                    id="cssInput" 
                    class="w-full h-32 p-4 border border-gray-300 rounded-lg code-block resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如: padding: 1rem; color: #3b82f6; display: flex;"
                >padding: 1rem; margin: 0.5rem; color: #3b82f6; display: flex; align-items: center;</textarea>
                
                <div class="mt-4 flex gap-4">
                    <button 
                        id="convertBtn" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                    >
                        转换
                    </button>
                    <button 
                        id="clearBtn" 
                        class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                    >
                        清空
                    </button>
                    <button 
                        id="exampleBtn" 
                        class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                    >
                        示例
                    </button>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Tailwind CSS 类名</h2>
                <div 
                    id="tailwindOutput" 
                    class="min-h-16 p-4 bg-gray-50 border border-gray-200 rounded-lg code-block"
                >
                    点击"转换"按钮查看结果...
                </div>
                
                <div class="mt-4 flex gap-4">
                    <button 
                        id="copyBtn" 
                        class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        disabled
                    >
                        复制类名
                    </button>
                    <button 
                        id="optimizeBtn" 
                        class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        disabled
                    >
                        优化类名
                    </button>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">样式预览</h2>
                <div class="border border-gray-200 rounded-lg p-4">
                    <div 
                        id="previewElement" 
                        class="bg-blue-100 p-4 rounded text-center"
                    >
                        预览元素 - 应用转换后的 Tailwind 类名
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-2">
                    这个元素会应用转换后的 Tailwind 类名，让你直观看到效果
                </p>
            </div>

            <!-- 功能说明 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">功能特性</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">✅ 支持的属性</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• 间距属性 (padding, margin)</li>
                            <li>• 布局属性 (display, position, flex)</li>
                            <li>• 尺寸属性 (width, height)</li>
                            <li>• 字体属性 (font-weight, text-align)</li>
                            <li>• 颜色属性 (color, background-color)</li>
                            <li>• 边框属性 (border, border-radius)</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">🚀 高级功能</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• 复合属性自动拆分</li>
                            <li>• 负值自动处理</li>
                            <li>• 任意值语法支持</li>
                            <li>• 类名冲突检测</li>
                            <li>• 智能优化建议</li>
                            <li>• 实时预览效果</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 转换器脚本 -->
    <script>
        // 内嵌简化版的转换器（用于演示）
        class SimpleCssToTailwindConverter {
            constructor() {
                this.spacingMap = {
                    '0px': '0', '1px': 'px', '0.125rem': '0.5', '0.25rem': '1',
                    '0.375rem': '1.5', '0.5rem': '2', '0.75rem': '3', '1rem': '4',
                    '1.25rem': '5', '1.5rem': '6', '2rem': '8', '2.5rem': '10',
                    '3rem': '12', '4rem': '16', '5rem': '20', '6rem': '24'
                }
                
                this.colorMap = {
                    '#000000': 'black', '#ffffff': 'white', '#f3f4f6': 'gray-100',
                    '#e5e7eb': 'gray-200', '#d1d5db': 'gray-300', '#9ca3af': 'gray-400',
                    '#6b7280': 'gray-500', '#374151': 'gray-600', '#1f2937': 'gray-700',
                    '#ef4444': 'red-500', '#3b82f6': 'blue-500', '#10b981': 'green-500'
                }
                
                this.propertyMap = {
                    'padding': 'p', 'padding-top': 'pt', 'padding-right': 'pr',
                    'padding-bottom': 'pb', 'padding-left': 'pl',
                    'margin': 'm', 'margin-top': 'mt', 'margin-right': 'mr',
                    'margin-bottom': 'mb', 'margin-left': 'ml',
                    'width': 'w', 'height': 'h', 'color': 'text',
                    'background-color': 'bg', 'border-radius': 'rounded'
                }
                
                this.specialValues = {
                    'display': { 'flex': 'flex', 'block': 'block', 'inline': 'inline', 'grid': 'grid' },
                    'align-items': { 'center': 'items-center', 'flex-start': 'items-start', 'flex-end': 'items-end' },
                    'justify-content': { 'center': 'justify-center', 'space-between': 'justify-between', 'flex-start': 'justify-start' },
                    'text-align': { 'center': 'text-center', 'left': 'text-left', 'right': 'text-right' },
                    'font-weight': { '700': 'font-bold', '600': 'font-semibold', '500': 'font-medium' }
                }
            }
            
            convertCssToTailwind(cssText) {
                const declarations = this.parseCssDeclarations(cssText)
                const tailwindClasses = []
                
                for (const { property, value } of declarations) {
                    const classes = this.convertProperty(property, value)
                    tailwindClasses.push(...classes)
                }
                
                return tailwindClasses.filter(Boolean)
            }
            
            parseCssDeclarations(cssText) {
                const declarations = []
                const rules = cssText.split(';').filter(rule => rule.trim())
                
                for (const rule of rules) {
                    const [property, value] = rule.split(':').map(s => s.trim())
                    if (property && value) {
                        declarations.push({ property, value })
                    }
                }
                
                return declarations
            }
            
            convertProperty(property, value) {
                if (this.specialValues[property] && this.specialValues[property][value]) {
                    return [this.specialValues[property][value]]
                }
                
                const prefix = this.propertyMap[property]
                if (!prefix) {
                    return [`[${property}:${value}]`]
                }
                
                if (property.includes('color')) {
                    const tailwindColor = this.colorMap[value.toLowerCase()]
                    return [`${prefix}-${tailwindColor || `[${value}]`}`]
                }
                
                if (property.includes('padding') || property.includes('margin')) {
                    const tailwindValue = this.spacingMap[value] || `[${value}]`
                    return [`${prefix}-${tailwindValue}`]
                }
                
                if (value.endsWith('%')) {
                    const num = parseFloat(value)
                    if (num === 50) return [`${prefix}-1/2`]
                    if (num === 25) return [`${prefix}-1/4`]
                    if (num === 75) return [`${prefix}-3/4`]
                    if (num === 100) return [`${prefix}-full`]
                }
                
                return [`${prefix}-[${value}]`]
            }
        }

        // 初始化转换器
        const converter = new SimpleCssToTailwindConverter()
        let lastConvertedClasses = []

        // DOM 元素
        const cssInput = document.getElementById('cssInput')
        const tailwindOutput = document.getElementById('tailwindOutput')
        const previewElement = document.getElementById('previewElement')
        const convertBtn = document.getElementById('convertBtn')
        const clearBtn = document.getElementById('clearBtn')
        const copyBtn = document.getElementById('copyBtn')
        const optimizeBtn = document.getElementById('optimizeBtn')
        const exampleBtn = document.getElementById('exampleBtn')

        // 转换函数
        function convertCss() {
            const cssText = cssInput.value.trim()
            if (!cssText) {
                tailwindOutput.textContent = '请输入 CSS 样式...'
                copyBtn.disabled = true
                optimizeBtn.disabled = true
                return
            }

            try {
                const classes = converter.convertCssToTailwind(cssText)
                lastConvertedClasses = classes
                
                if (classes.length === 0) {
                    tailwindOutput.textContent = '未找到可转换的样式'
                } else {
                    tailwindOutput.innerHTML = classes.map(cls => 
                        `<span class="highlight">${cls}</span>`
                    ).join(' ')
                    
                    // 更新预览
                    previewElement.className = 'bg-blue-100 p-4 rounded text-center ' + classes.join(' ')
                }
                
                copyBtn.disabled = classes.length === 0
                optimizeBtn.disabled = classes.length === 0
            } catch (error) {
                tailwindOutput.textContent = `错误: ${error.message}`
                copyBtn.disabled = true
                optimizeBtn.disabled = true
            }
        }

        // 事件监听器
        convertBtn.addEventListener('click', convertCss)
        
        clearBtn.addEventListener('click', () => {
            cssInput.value = ''
            tailwindOutput.textContent = '点击"转换"按钮查看结果...'
            previewElement.className = 'bg-blue-100 p-4 rounded text-center'
            copyBtn.disabled = true
            optimizeBtn.disabled = true
        })

        copyBtn.addEventListener('click', async () => {
            if (lastConvertedClasses.length > 0) {
                try {
                    await navigator.clipboard.writeText(lastConvertedClasses.join(' '))
                    copyBtn.textContent = '已复制!'
                    setTimeout(() => {
                        copyBtn.textContent = '复制类名'
                    }, 2000)
                } catch (err) {
                    alert('复制失败，请手动复制')
                }
            }
        })

        exampleBtn.addEventListener('click', () => {
            const examples = [
                'padding: 2rem; margin: 1rem; background-color: #ffffff; border-radius: 0.5rem;',
                'display: flex; align-items: center; justify-content: space-between; width: 100%;',
                'font-weight: 700; text-align: center; color: #ef4444; font-size: 1.5rem;',
                'position: absolute; top: 0; right: 0; z-index: 50; opacity: 0.9;'
            ]
            const randomExample = examples[Math.floor(Math.random() * examples.length)]
            cssInput.value = randomExample
            convertCss()
        })

        // 实时转换（可选）
        cssInput.addEventListener('input', () => {
            clearTimeout(cssInput.timeout)
            cssInput.timeout = setTimeout(convertCss, 500)
        })

        // 初始转换
        convertCss()
    </script>
</body>
</html>
